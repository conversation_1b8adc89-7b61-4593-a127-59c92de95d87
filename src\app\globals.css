@import "tailwindcss";

/* Let <PERSON><PERSON> handle the dark theme colors */
:root {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Override any conflicting styles to ensure Mantine dark theme works */
body {
  font-family: Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif;
}

/* Ensure containers and papers use <PERSON><PERSON>'s dark theme colors */
[data-mantine-color-scheme="dark"] {
  --mantine-color-body: var(--mantine-color-dark-7);
  --mantine-color-text: var(--mantine-color-dark-0);
}

/* Fix any white backgrounds in dark mode */
[data-mantine-color-scheme="dark"] .mantine-Container-root,
[data-mantine-color-scheme="dark"] .mantine-Paper-root,
[data-mantine-color-scheme="dark"] .mantine-Card-root {
  background-color: var(--mantine-color-dark-6);
  color: var(--mantine-color-dark-0);
}
