'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Center,
  Paper,
  Group,
  ThemeIcon,
  List,
  Anchor,
} from '@mantine/core';
import { IconWallet, IconDashboard } from '@tabler/icons-react';
import { ConnectWallet } from '@/components/Wallet/ConnectWallet';

export default function Home() {
  const { isConnected } = useAccount();
  const router = useRouter();

  useEffect(() => {
    if (isConnected) {
      router.push('/dashboard');
    }
  }, [isConnected, router]);

  return (
    <Container size="md" py={80}>
      <Center>
        <Stack align="center" gap="xl">
          <ThemeIcon size={80} radius="xl" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }}>
            <IconWallet size={40} />
          </ThemeIcon>

          <Stack align="center" gap="md">
            <Title order={1} size="h1" ta="center">
              Welcome to ODude Manager
            </Title>
            <Text size="xl" c="dimmed" ta="center" maw={600}>
              Your comprehensive Web3 dashboard for managing ODude Names across multiple blockchain networks
            </Text>
          </Stack>

          <Paper shadow="md" p="xl" radius="lg" withBorder>
            <Stack align="center" gap="lg">
              <Text size="lg" fw={500}>
                Connect your wallet to get started
              </Text>
              <ConnectWallet />
            </Stack>
          </Paper>

          <Paper p="lg" radius="md" withBorder>
            <Stack gap="md">
              <Text size="lg" fw={500} ta="center">
                What you can do with ODude Manager:
              </Text>
              <List
                gap="sm"
                size="sm"
                center
                icon={
                  <ThemeIcon c="blue" size={24} radius="xl">
                    <IconDashboard size={16} />
                  </ThemeIcon>
                }
              >
                <List.Item>View your wallet balance and network status</List.Item>
                <List.Item>Manage all your ODude Names in one place</List.Item>
                <List.Item>Search and discover available domain names</List.Item>
                <List.Item>Mint new domains with detailed pricing information</List.Item>
                <List.Item>Monitor transactions across multiple networks</List.Item>
              </List>
            </Stack>
          </Paper>

          <Group gap="md">
            <Text size="sm" c="dimmed">
              Powered by{' '}
              <Anchor href="https://github.com/odude/odude-sdk" target="_blank">
                ODude SDK
              </Anchor>
              {' • '}
              <Anchor href="https://rainbowkit.com" target="_blank">
                RainbowKit
              </Anchor>
              {' • '}
              <Anchor href="https://mantine.dev" target="_blank">
                Mantine
              </Anchor>
            </Text>
          </Group>
        </Stack>
      </Center>
    </Container>
  );
}
