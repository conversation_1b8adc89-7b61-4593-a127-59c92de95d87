import ODudeSDK, { ODudeSDKConfig } from '@odude/odude-sdk';
import { mainnet, polygon, optimism, arbitrum, baseSepolia } from 'wagmi/chains';

// SDK Configuration
const config: ODudeSDKConfig = {
  rpcUrl_sepolia: process.env.NEXT_PUBLIC_BASE_SEPOLIA_RPC_URL || 'https://sepolia.base.org',
  rpcUrl_filecoin: process.env.NEXT_PUBLIC_FILECOIN_RPC_URL || 'https://api.node.glif.io',
  rpcUrl_bnb: process.env.NEXT_PUBLIC_BNB_RPC_URL || 'https://bsc-dataseed1.binance.org',
};

// Create SDK instance
export const sdk = new ODudeSDK(config);

// Initialize SDK (call this once in your app)
let initialized = false;
export const initializeSDK = async () => {
  if (!initialized) {
    try {
      await sdk.connectAllNetworks();
      initialized = true;
      console.log('✓ ODude SDK initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize ODude SDK:', error);
      throw error;
    }
  }
  return sdk;
};

// Helper function to get network info
export const getNetworkInfo = () => {
  return sdk.NetworkList();
};

// Helper function to check if SDK is ready
export const isSDKReady = () => initialized;

// Helper function to get selected network from session storage
export const getSelectedNetwork = (): string | null => {
  if (typeof window === 'undefined') return null;
  return sessionStorage.getItem('selectedODudeNetwork');
};

// Helper function to set selected network in session storage
export const setSelectedNetwork = (networkKey: string): void => {
  if (typeof window === 'undefined') return;
  sessionStorage.setItem('selectedODudeNetwork', networkKey);
};

// Network mapping between ODude networks and Wagmi chain IDs
export const NETWORK_CHAIN_MAPPING: Record<string, number> = {
  'basesepolia': baseSepolia.id,
  'mainnet': mainnet.id,
  'polygon': polygon.id,
  'optimism': optimism.id,
  'arbitrum': arbitrum.id,
};

// Reverse mapping from chain ID to ODude network
export const CHAIN_NETWORK_MAPPING: Record<number, string> = Object.fromEntries(
  Object.entries(NETWORK_CHAIN_MAPPING).map(([network, chainId]) => [chainId, network])
);

// Helper function to get available networks for display
export const displayNetworkList = () => {
  if (!initialized) {
    return {
      currentNetwork: null,
      availableNetworks: [],
      supportedNetworks: {}
    };
  }

  const networkInfo = getNetworkInfo();
  const selectedNetwork = getSelectedNetwork();

  return {
    currentNetwork: selectedNetwork || networkInfo.currentNetwork,
    availableNetworks: Object.keys(networkInfo.supportedNetworks),
    supportedNetworks: networkInfo.supportedNetworks
  };
};

// Helper function to get ODude network from chain ID
export const getODudeNetworkFromChainId = (chainId: number): string | null => {
  return CHAIN_NETWORK_MAPPING[chainId] || null;
};

// Helper function to get chain ID from ODude network
export const getChainIdFromODudeNetwork = (network: string): number | null => {
  return NETWORK_CHAIN_MAPPING[network] || null;
};
