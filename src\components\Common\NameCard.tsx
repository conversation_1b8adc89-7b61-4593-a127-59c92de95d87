'use client';

import {
  Card,
  Text,
  Group,
  Badge,
  Stack,
  Button,
  ActionIcon,
  Divider,
} from '@mantine/core';
import { IconCopy, IconExternalLink } from '@tabler/icons-react';
import { useClipboard } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';

interface NameCardProps {
  name: string;
  tokenId: string;
  owner?: string;
  resolvedAddress?: string;
  metadata?: Record<string, unknown>;
  status?: 'owned' | 'available' | 'taken';
  onViewDetails?: () => void;
  onMint?: () => void;
  mintCost?: string;
}

export function NameCard({
  name,
  tokenId,
  owner,
  resolvedAddress,
  metadata,
  status = 'owned',
  onViewDetails,
  onMint,
  mintCost,
}: NameCardProps) {
  const clipboard = useClipboard({ timeout: 500 });

  const handleCopyAddress = (addr: string) => {
    clipboard.copy(addr);
    notifications.show({
      title: 'Address Copied',
      message: 'Address copied to clipboard',
      color: 'green',
    });
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'owned':
        return <Badge variant="light" color="blue">Owned</Badge>;
      case 'available':
        return <Badge variant="light" color="green">Available</Badge>;
      case 'taken':
        return <Badge variant="light" color="red">Taken</Badge>;
      default:
        return null;
    }
  };

  return (
    <Card shadow="sm" p="lg" radius="md" withBorder>
      <Stack gap="md">
        <Group justify="space-between">
          <div>
            <Text fw={600} size="lg">{name}</Text>
            <Text size="sm" c="dimmed">Token ID: {tokenId}</Text>
          </div>
          {getStatusBadge()}
        </Group>

        <Divider />

        <Stack gap="xs">
          {resolvedAddress && (
            <Group justify="space-between">
              <Text size="sm" c="dimmed">Resolves to:</Text>
              <Group gap="xs">
                <Text size="sm" fw={500}>
                  {formatAddress(resolvedAddress)}
                </Text>
                <ActionIcon
                  size="sm"
                  variant="subtle"
                  onClick={() => handleCopyAddress(resolvedAddress)}
                >
                  <IconCopy size={12} />
                </ActionIcon>
              </Group>
            </Group>
          )}

          {owner && (
            <Group justify="space-between">
              <Text size="sm" c="dimmed">Owner:</Text>
              <Group gap="xs">
                <Text size="sm" fw={500}>
                  {formatAddress(owner)}
                </Text>
                <ActionIcon
                  size="sm"
                  variant="subtle"
                  onClick={() => handleCopyAddress(owner)}
                >
                  <IconCopy size={12} />
                </ActionIcon>
              </Group>
            </Group>
          )}

          {metadata?.description && (
            <div>
              <Text size="sm" c="dimmed">Description:</Text>
              <Text size="sm">{metadata.description}</Text>
            </div>
          )}

          {mintCost && status === 'available' && (
            <Group justify="space-between">
              <Text size="sm" c="dimmed">Mint Cost:</Text>
              <Text size="sm" fw={500}>{mintCost} ETH</Text>
            </Group>
          )}
        </Stack>

        <Group justify="flex-end" mt="md">
          {status === 'available' && onMint && (
            <Button
              size="sm"
              variant="gradient"
              gradient={{ from: 'green', to: 'teal' }}
              onClick={onMint}
            >
              Mint ({mintCost} ETH)
            </Button>
          )}
          
          {onViewDetails && (
            <Button
              size="sm"
              variant="outline"
              leftSection={<IconExternalLink size={14} />}
              onClick={onViewDetails}
            >
              View Details
            </Button>
          )}
        </Group>
      </Stack>
    </Card>
  );
}
