import { getDefaultConfig } from '@rainbow-me/rainbowkit'
import { mainnet, polygon, optimism, arbitrum, baseSepolia } from 'wagmi/chains'

export const config = getDefaultConfig({
  appName: 'ODude Manager',
  projectId: process.env.NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID || 'your-project-id',
  chains: [mainnet, polygon, optimism, arbitrum, baseSepolia],
  ssr: true, // enable if using Next.js App Router
})
