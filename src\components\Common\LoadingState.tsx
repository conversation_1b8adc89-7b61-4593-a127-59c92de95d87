'use client';

import { Center, Stack, Loader, Text } from '@mantine/core';

interface LoadingStateProps {
  message?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export function LoadingState({ message = 'Loading...', size = 'lg' }: LoadingStateProps) {
  return (
    <Center py="xl">
      <Stack align="center" gap="md">
        <Loader size={size} />
        <Text c="dimmed">{message}</Text>
      </Stack>
    </Center>
  );
}
