/**
 * Basic component tests
 */

import { render, screen } from '@testing-library/react';
import { MantineProvider } from '@mantine/core';
import { NameCard } from '../src/components/Common/NameCard';
import { LoadingState } from '../src/components/Common/LoadingState';
import { EmptyState } from '../src/components/Common/EmptyState';
import { IconWallet } from '@tabler/icons-react';

// Mock notifications
jest.mock('@mantine/notifications', () => ({
  notifications: {
    show: jest.fn(),
  },
}));

// Mock clipboard
jest.mock('@mantine/hooks', () => ({
  useClipboard: () => ({
    copy: jest.fn(),
  }),
}));

const renderWithMantine = (component) => {
  return render(
    <MantineProvider>
      {component}
    </MantineProvider>
  );
};

describe('NameCard Component', () => {
  test('renders name card with basic props', () => {
    renderWithMantine(
      <NameCard
        name="test@crypto"
        tokenId="123"
        status="owned"
      />
    );
    
    expect(screen.getByText('test@crypto')).toBeInTheDocument();
    expect(screen.getByText('Token ID: 123')).toBeInTheDocument();
    expect(screen.getByText('Owned')).toBeInTheDocument();
  });

  test('renders available name with mint option', () => {
    const mockMint = jest.fn();
    
    renderWithMantine(
      <NameCard
        name="available@crypto"
        tokenId="456"
        status="available"
        mintCost="0.1"
        onMint={mockMint}
      />
    );
    
    expect(screen.getByText('Available')).toBeInTheDocument();
    expect(screen.getByText('Mint (0.1 ETH)')).toBeInTheDocument();
  });
});

describe('LoadingState Component', () => {
  test('renders loading state with default message', () => {
    renderWithMantine(<LoadingState />);
    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  test('renders loading state with custom message', () => {
    renderWithMantine(<LoadingState message="Fetching names..." />);
    expect(screen.getByText('Fetching names...')).toBeInTheDocument();
  });
});

describe('EmptyState Component', () => {
  test('renders empty state with all props', () => {
    const mockAction = jest.fn();
    
    renderWithMantine(
      <EmptyState
        icon={IconWallet}
        title="No Names Found"
        description="You don't have any names yet"
        actionLabel="Search Names"
        onAction={mockAction}
      />
    );
    
    expect(screen.getByText('No Names Found')).toBeInTheDocument();
    expect(screen.getByText("You don't have any names yet")).toBeInTheDocument();
    expect(screen.getByText('Search Names')).toBeInTheDocument();
  });
});
