/**
 * Basic tests for ODude SDK integration
 */

import { initializeSDK, getNetworkInfo } from '../src/lib/odude';

// Mock the ODude SDK
jest.mock('@odude/odude-sdk', () => {
  return jest.fn().mockImplementation(() => ({
    connectAllNetworks: jest.fn().mockResolvedValue(true),
    NetworkList: jest.fn().mockReturnValue({
      currentNetwork: 'basesepolia',
      connectedNetworks: ['basesepolia'],
      supportedNetworks: {
        basesepolia: {
          name: 'Base Sepolia',
          isConnected: true,
          hasContracts: true,
        },
      },
    }),
    getTotalNames: jest.fn().mockResolvedValue(0n),
    getNamesList: jest.fn().mockResolvedValue([]),
    resolver: jest.fn().mockReturnValue({
      nameExists: jest.fn().mockResolvedValue(false),
    }),
  }));
});

describe('ODude SDK Integration', () => {
  test('should initialize SDK successfully', async () => {
    const sdk = await initializeSDK();
    expect(sdk).toBeDefined();
  });

  test('should get network information', () => {
    const networkInfo = getNetworkInfo();
    expect(networkInfo).toBeDefined();
    expect(networkInfo.currentNetwork).toBe('basesepolia');
  });

  test('should handle SDK errors gracefully', async () => {
    // This test ensures error handling works
    try {
      await initializeSDK();
    } catch (error) {
      expect(error).toBeInstanceOf(Error);
    }
  });
});

describe('Network Configuration', () => {
  test('should have correct network configuration', () => {
    const networkInfo = getNetworkInfo();
    expect(networkInfo.supportedNetworks).toBeDefined();
    expect(networkInfo.supportedNetworks.basesepolia).toBeDefined();
  });
});
