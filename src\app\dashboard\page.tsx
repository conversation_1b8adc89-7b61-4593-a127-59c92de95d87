'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount, useChainId, useBalance } from 'wagmi';
import { mainnet, polygon, optimism, arbitrum, baseSepolia } from 'wagmi/chains';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  Badge,
  ThemeIcon,
  Loader,
  Alert,
  SimpleGrid,
  Card,
} from '@mantine/core';
import {
  IconWallet,
  IconNetwork,
  IconAlertCircle,
  IconCheck,
  IconX,
  IconDashboard,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, getNetworkInfo } from '@/lib/odude';

export default function Dashboard() {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { data: balance, isLoading: balanceLoading } = useBalance({ address });
  const router = useRouter();

  // Get current chain info
  const chains = [mainnet, polygon, optimism, arbitrum, baseSepolia];
  const currentChain = chains.find(chain => chain.id === chainId);
  
  const [sdkInitialized, setSdkInitialized] = useState(false);
  const [sdkError, setSdkError] = useState<string | null>(null);
  const [networkInfo, setNetworkInfo] = useState<{
    currentNetwork: string;
    connectedNetworks: string[];
    supportedNetworks: Record<string, { name: string; isConnected: boolean; hasContracts: boolean }>;
  } | null>(null);

  useEffect(() => {
    if (!isConnected) {
      router.push('/');
      return;
    }

    // Initialize SDK
    const initSDK = async () => {
      try {
        await initializeSDK();
        setSdkInitialized(true);
        const info = getNetworkInfo();
        setNetworkInfo(info as {
          currentNetwork: string;
          connectedNetworks: string[];
          supportedNetworks: Record<string, { name: string; isConnected: boolean; hasContracts: boolean }>;
        });
      } catch (error) {
        console.error('Failed to initialize SDK:', error);
        setSdkError(error instanceof Error ? error.message : 'Unknown error');
      }
    };

    initSDK();
  }, [isConnected, router]);

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatBalance = (bal: { formatted: string } | undefined) => {
    if (!bal) return '0';
    return parseFloat(bal.formatted).toFixed(4);
  };

  if (!isConnected) {
    return null; // Will redirect
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }}>
              <IconDashboard size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Dashboard</Title>
              <Text c="dimmed">Welcome to your Web3 dashboard</Text>
            </div>
          </Group>

          {/* Wallet Information */}
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Stack gap="md">
              <Group>
                <ThemeIcon color="blue" size={30}>
                  <IconWallet size={18} />
                </ThemeIcon>
                <Text size="lg" fw={500}>Wallet Information</Text>
              </Group>
              
              <SimpleGrid cols={{ base: 1, sm: 3 }}>
                <Card p="md" withBorder>
                  <Stack gap="xs">
                    <Text size="sm" c="dimmed">Address</Text>
                    <Text fw={500} size="sm">{formatAddress(address!)}</Text>
                  </Stack>
                </Card>

                <Card p="md" withBorder>
                  <Stack gap="xs">
                    <Text size="sm" c="dimmed">Network</Text>
                    <Badge variant="light" color="blue">
                      {currentChain?.name || 'Unknown'}
                    </Badge>
                  </Stack>
                </Card>

                <Card p="md" withBorder>
                  <Stack gap="xs">
                    <Text size="sm" c="dimmed">Balance</Text>
                    {balanceLoading ? (
                      <Loader size="sm" />
                    ) : (
                      <Text fw={500} size="sm">
                        {formatBalance(balance)} {balance?.symbol || 'ETH'}
                      </Text>
                    )}
                  </Stack>
                </Card>
              </SimpleGrid>
            </Stack>
          </Paper>

          {/* SDK Status */}
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Stack gap="md">
              <Group>
                <ThemeIcon color="green" size={30}>
                  <IconNetwork size={18} />
                </ThemeIcon>
                <Text size="lg" fw={500}>ODude SDK Status</Text>
              </Group>
              
              {sdkError ? (
                <Alert icon={<IconAlertCircle size={16} />} title="SDK Error" color="red">
                  {sdkError}
                </Alert>
              ) : sdkInitialized ? (
                <Alert icon={<IconCheck size={16} />} title="SDK Ready" color="green">
                  ODude SDK is connected and ready to use
                </Alert>
              ) : (
                <Alert icon={<Loader size={16} />} title="Initializing" color="blue">
                  Connecting to ODude networks...
                </Alert>
              )}
            </Stack>
          </Paper>

          {/* Network Information */}
          {networkInfo && (
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Stack gap="md">
                <Group>
                  <ThemeIcon color="violet" size={30}>
                    <IconNetwork size={18} />
                  </ThemeIcon>
                  <Text size="lg" fw={500}>Network Status</Text>
                </Group>

                <SimpleGrid cols={{ base: 1, sm: 2 }}>
                  <Card p="md" withBorder>
                    <Stack gap="xs">
                      <Text size="sm" c="dimmed">Current Network</Text>
                      <Badge variant="light" color="blue">
                        {networkInfo.currentNetwork}
                      </Badge>
                    </Stack>
                  </Card>

                  <Card p="md" withBorder>
                    <Stack gap="xs">
                      <Text size="sm" c="dimmed">Connected Networks</Text>
                      <Text size="sm">{networkInfo.connectedNetworks.join(', ')}</Text>
                    </Stack>
                  </Card>
                </SimpleGrid>

                <div>
                  <Text size="sm" c="dimmed" mb="xs">Supported Networks</Text>
                  <Stack gap="xs">
                    {Object.entries(networkInfo.supportedNetworks).map(([key, network]) => (
                      <Group key={key} justify="space-between">
                        <Group gap="xs">
                          <Text size="sm">{network.name}</Text>
                          {network.isConnected ? (
                            <IconCheck size={16} color="green" />
                          ) : (
                            <IconX size={16} color="red" />
                          )}
                        </Group>
                        <Badge
                          variant="outline"
                          color={network.hasContracts ? 'green' : 'orange'}
                          size="xs"
                        >
                          {network.hasContracts ? 'Deployed' : 'No Contracts'}
                        </Badge>
                      </Group>
                    ))}
                  </Stack>
                </div>
              </Stack>
            </Paper>
          )}

          {/* Quick Actions */}
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Stack gap="md">
              <Text size="lg" fw={500}>Quick Actions</Text>
              <SimpleGrid cols={{ base: 1, sm: 2 }}>
                <Card
                  p="md"
                  withBorder
                  style={{ cursor: 'pointer' }}
                  onClick={() => router.push('/my-names')}
                >
                  <Stack align="center" gap="xs">
                    <ThemeIcon color="blue" size={40}>
                      <IconWallet size={24} />
                    </ThemeIcon>
                    <Text fw={500}>My Names</Text>
                    <Text size="sm" c="dimmed" ta="center">
                      View and manage your ODude names
                    </Text>
                  </Stack>
                </Card>

                <Card
                  p="md"
                  withBorder
                  style={{ cursor: 'pointer' }}
                  onClick={() => router.push('/search')}
                >
                  <Stack align="center" gap="xs">
                    <ThemeIcon color="green" size={40}>
                      <IconNetwork size={24} />
                    </ThemeIcon>
                    <Text fw={500}>Search Names</Text>
                    <Text size="sm" c="dimmed" ta="center">
                      Find and mint new domain names
                    </Text>
                  </Stack>
                </Card>
              </SimpleGrid>
            </Stack>
          </Paper>
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
