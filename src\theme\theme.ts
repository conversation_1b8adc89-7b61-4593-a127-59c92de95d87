import { createTheme, MantineThemeOverride } from '@mantine/core';

export const theme: MantineThemeOverride = createTheme({
  primaryColor: 'blue',
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
  headings: {
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
  },
  components: {
    Button: {
      styles: {
        root: {
          borderRadius: '8px',
        },
      },
    },
    Card: {
      styles: {
        root: {
          borderRadius: '12px',
          backgroundColor: 'var(--mantine-color-dark-6)',
          borderColor: 'var(--mantine-color-dark-4)',
        },
      },
    },
    Paper: {
      styles: {
        root: {
          backgroundColor: 'var(--mantine-color-dark-6)',
          borderColor: 'var(--mantine-color-dark-4)',
        },
      },
    },
    Container: {
      styles: {
        root: {
          backgroundColor: 'transparent',
        },
      },
    },
    TextInput: {
      styles: {
        input: {
          borderRadius: '8px',
        },
      },
    },
    NavLink: {
      styles: {
        root: {
          borderRadius: '8px',
          '&[data-active]': {
            backgroundColor: 'var(--mantine-color-blue-light)',
            color: 'var(--mantine-color-blue-light-color)',
          },
        },
        label: {
          color: 'var(--mantine-color-text)',
        },
      },
    },
  },
});
