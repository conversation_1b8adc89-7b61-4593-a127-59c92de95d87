'use client';

import { Group, Badge, Tooltip, ActionIcon } from '@mantine/core';
import { IconWallet, IconCopy } from '@tabler/icons-react';
import { useAccount, useChainId, useBalance } from 'wagmi';
import { mainnet, polygon, optimism, arbitrum, baseSepolia } from 'wagmi/chains';
import { useClipboard } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';

export function WalletInfo() {
  const { address, isConnected } = useAccount();
  const chainId = useChainId();
  const { data: balance } = useBalance({ address });
  const clipboard = useClipboard({ timeout: 500 });

  // Get current chain info
  const chains = [mainnet, polygon, optimism, arbitrum, baseSepolia];
  const currentChain = chains.find(chain => chain.id === chainId);

  const handleCopyAddress = () => {
    if (address) {
      clipboard.copy(address);
      notifications.show({
        title: 'Address Copied',
        message: 'Wallet address copied to clipboard',
        color: 'green',
      });
    }
  };

  if (!isConnected || !address) {
    return null;
  }

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatBalance = (bal: { formatted: string } | undefined) => {
    if (!bal) return '0';
    return parseFloat(bal.formatted).toFixed(4);
  };

  return (
    <Group gap="xs">
      <Tooltip label="Connected Wallet">
        <Badge
          variant="light"
          leftSection={<IconWallet size={14} />}
          rightSection={
            <ActionIcon
              size="xs"
              variant="transparent"
              onClick={handleCopyAddress}
            >
              <IconCopy size={12} />
            </ActionIcon>
          }
        >
          {formatAddress(address)}
        </Badge>
      </Tooltip>
      
      {currentChain && (
        <Tooltip label={`Connected to ${currentChain.name}`}>
          <Badge variant="outline" c="blue">
            {currentChain.name}
          </Badge>
        </Tooltip>
      )}
      
      {balance && (
        <Tooltip label="Wallet Balance">
          <Badge variant="outline" c="green">
            {formatBalance(balance)} {balance.symbol}
          </Badge>
        </Tooltip>
      )}
    </Group>
  );
}
