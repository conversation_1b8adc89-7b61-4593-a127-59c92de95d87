'use client';

import { Paper, Center, Stack, ThemeIcon, Text, Button } from '@mantine/core';
import { IconProps } from '@tabler/icons-react';

interface EmptyStateProps {
  icon: React.ComponentType<IconProps>;
  title: string;
  description: string;
  actionLabel?: string;
  onAction?: () => void;
}

export function EmptyState({
  icon: Icon,
  title,
  description,
  actionLabel,
  onAction,
}: EmptyStateProps) {
  return (
    <Paper shadow="sm" p="xl" radius="md" withBorder>
      <Center>
        <Stack align="center" gap="md">
          <ThemeIcon size={60} radius="xl" color="gray">
            <Icon size={30} />
          </ThemeIcon>
          <div style={{ textAlign: 'center' }}>
            <Text size="lg" fw={500}>{title}</Text>
            <Text c="dimmed" mt="xs">
              {description}
            </Text>
          </div>
          {actionLabel && onAction && (
            <Button
              variant="gradient"
              gradient={{ from: 'blue', to: 'cyan' }}
              onClick={onAction}
            >
              {actionLabel}
            </Button>
          )}
        </Stack>
      </Center>
    </Paper>
  );
}
