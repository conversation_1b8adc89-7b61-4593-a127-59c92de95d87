# ODude Manager - Web3 Dashboard

A comprehensive Next.js application for managing ODude Names across multiple blockchain networks. Built with TypeScript, Mantine UI, and RainbowKit for seamless Web3 integration.

## 🚀 Features

- **🪪 Wallet Integration**: Connect with MetaMask, WalletConnect, and other popular wallets via RainbowKit
- **🌐 Multi-Network Support**: Base Sepolia, Filecoin, BNB Chain support through ODude SDK
- **📱 Responsive Design**: Dark theme by default with Mantine AppShell layout
- **🔍 Name Search**: Search and discover available ODude domain names
- **💼 Portfolio Management**: View and manage all your owned ODude names
- **⚡ Real-time Updates**: Live wallet balance and network status
- **🎯 Minting Interface**: Mint new domains with detailed pricing information

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with TypeScript
- **UI Library**: Mantine v7 (Dark theme)
- **Web3 Integration**: RainbowKit + Wagmi
- **Blockchain SDK**: @odude/odude-sdk
- **Styling**: Tailwind CSS + Mantine components
- **Testing**: Jest + React Testing Library

## 📦 Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment variables**:
   Update `.env.local` with your values:
   ```bash
   # RPC URLs for different networks
   NEXT_PUBLIC_BASE_SEPOLIA_RPC_URL=https://sepolia.base.org
   NEXT_PUBLIC_FILECOIN_RPC_URL=https://api.node.glif.io
   NEXT_PUBLIC_BNB_RPC_URL=https://bsc-dataseed1.binance.org

   # Wallet Connect Project ID (get from https://cloud.walletconnect.com/)
   NEXT_PUBLIC_WALLET_CONNECT_PROJECT_ID=your-project-id-here
   ```

3. **Run the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser**:
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🧭 App Structure

### Pages
- **`/`** - Landing page with wallet connection
- **`/dashboard`** - Main dashboard with wallet info and network status
- **`/my-names`** - Portfolio view of owned ODude names
- **`/search`** - Search and mint new domain names

### Key Components
- **`AppShellLayout`** - Main layout with sidebar navigation
- **`ConnectWallet`** - RainbowKit wallet connection component
- **`WalletInfo`** - Display connected wallet information
- **`NameCard`** - Reusable component for displaying domain information

## 🧪 Testing

```bash
npm test              # Run all tests
npm run test:watch    # Run tests in watch mode
```

## 🚀 Deployment

```bash
npm run build
npm start
```

Built with ❤️ for the ODude ecosystem
