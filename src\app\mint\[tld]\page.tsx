'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount, useWalletClient, useBalance } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  ThemeIcon,
  Button,
  Alert,
  Card,
  Badge,
  Divider,
  Center,
  Loader,
  SimpleGrid,
  List,
} from '@mantine/core';
import {
  IconCoin,
  IconAlertCircle,
  IconCheck,
  IconWallet,
  IconArrowLeft,
  IconInfoCircle,
  IconTrendingUp,
  IconUsers,
  IconShield,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';
import { notifications } from '@mantine/notifications';

interface TldMintInfo {
  tldName: string;
  exists: boolean;
  mintCost?: bigint;
  benefits: string[];
  requirements: string[];
  tldInfo?: Record<string, unknown>;
}

interface PageProps {
  params: Promise<{
    tld: string;
  }> | {
    tld: string;
  };
}

export default function TldMintPage({ params }: PageProps) {
  const [tld, setTld] = useState<string>('');

  // Handle async params (Next.js 15 compatibility)
  useEffect(() => {
    const getParams = async () => {
      try {
        // Check if params is a Promise
        const resolvedParams = params instanceof Promise ? await params : params;
        setTld(resolvedParams.tld);
      } catch (error) {
        console.error('Error resolving params:', error);
        setTld('');
      }
    };
    getParams();
  }, [params]);

  const { address, isConnected } = useAccount();
  const { data: walletClient } = useWalletClient();
  const { data: balance } = useBalance({ address });
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [minting, setMinting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mintInfo, setMintInfo] = useState<TldMintInfo | null>(null);
  const [sdkReady, setSdkReady] = useState(false);

  const fetchTldMintInfo = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const decodedTld = decodeURIComponent(tld);
      console.log('Fetching TLD mint info for:', decodedTld);

      let tldExists = false;
      let tldData = null;

      try {
        // Check if TLD already exists
        tldData = await sdk.getTldInfo(decodedTld);
        tldExists = true;
      } catch (err) {
        console.log('TLD does not exist, can be minted:', err);
        tldExists = false;
      }

      // Default TLD benefits and requirements
      const benefits = [
        'Become the owner of the TLD and earn commission from all domain registrations',
        'Set custom pricing for domains under your TLD',
        'Control TLD policies and registration rules',
        'Receive ongoing revenue from domain renewals',
        'Build your own namespace ecosystem',
        'Priority access to new features and tools'
      ];

      const requirements = [
        'Connect your wallet to the Base Sepolia network',
        'Have sufficient ETH balance for the minting transaction',
        'TLD name must be available (not already registered)',
        'TLD name must follow naming conventions (alphanumeric, no special characters)',
        'Minimum 3 characters, maximum 10 characters for TLD name'
      ];

      // Estimate mint cost (this would come from the SDK in a real implementation)
      const estimatedMintCost = BigInt('100000000000000000'); // 0.1 ETH

      setMintInfo({
        tldName: decodedTld,
        exists: tldExists,
        mintCost: tldExists ? undefined : estimatedMintCost,
        benefits,
        requirements,
        tldInfo: tldData,
      });

    } catch (error) {
      console.error('Failed to fetch TLD mint info:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch TLD information');
    } finally {
      setLoading(false);
    }
  }, [tld]);

  useEffect(() => {
    if (!isConnected) {
      router.push('/');
      return;
    }

    initializeSDK()
      .then(() => setSdkReady(true))
      .catch((err) => setError(err.message));
  }, [isConnected, router]);

  useEffect(() => {
    if (sdkReady && tld) {
      fetchTldMintInfo();
    }
  }, [sdkReady, tld, fetchTldMintInfo]);

  const formatEther = (wei: bigint) => {
    return (Number(wei) / 1e18).toFixed(4);
  };

  const handleMintTld = async () => {
    if (!mintInfo || mintInfo.exists || !walletClient || !address || !mintInfo.mintCost) {
      notifications.show({
        title: 'Cannot Mint TLD',
        message: 'TLD already exists or wallet not connected',
        color: 'red',
      });
      return;
    }

    // Check if wallet has sufficient balance
    if (balance && mintInfo.mintCost > balance.value) {
      notifications.show({
        title: 'Insufficient Balance',
        message: `You need ${formatEther(mintInfo.mintCost)} ETH but only have ${balance.formatted} ETH`,
        color: 'red',
      });
      return;
    }

    setMinting(true);

    try {
      // Initialize SDK if needed
      await initializeSDK();

      notifications.show({
        title: 'TLD Minting Started',
        message: 'Please confirm the transaction in your wallet',
        color: 'blue',
      });

      // Mint the TLD using the SDK
      // Note: This is a placeholder implementation. Replace with actual SDK method when available.
      try {
        // Try to use the TLD minting method if it exists
        const tx = await sdk.tld().mintTLD(
          mintInfo.tldName,
          address,
          {
            value: mintInfo.mintCost,
          }
        );

        // Wait for transaction confirmation
        await tx.wait();
      } catch (methodError) {
        // If the method doesn't exist, show a helpful message
        console.warn('TLD minting method not available:', methodError);
        throw new Error('TLD minting functionality is not yet implemented in the SDK. Please check back later.');
      }

      notifications.show({
        title: 'TLD Minting Successful!',
        message: `Successfully minted TLD "${mintInfo.tldName}"`,
        color: 'green',
      });

      // Redirect to the info page for the newly minted TLD
      router.push(`/info/${encodeURIComponent(mintInfo.tldName)}`);

    } catch (error) {
      console.error('TLD minting failed:', error);
      notifications.show({
        title: 'TLD Minting Failed',
        message: error instanceof Error ? error.message : 'Failed to mint TLD',
        color: 'red',
      });
    } finally {
      setMinting(false);
    }
  };

  if (!isConnected || !tld) {
    return null; // Will redirect or still loading params
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => router.back()}
            >
              Back
            </Button>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'orange', to: 'red' }}>
              <IconCoin size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Mint TLD</Title>
              <Text c="dimmed">Register and own the "{decodeURIComponent(tld)}" TLD</Text>
            </div>
          </Group>

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
              {error}
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Loading TLD information...</Text>
              </Stack>
            </Paper>
          )}

          {/* TLD Already Exists */}
          {!loading && !error && mintInfo?.exists && (
            <Alert icon={<IconInfoCircle size={16} />} title="TLD Already Registered" color="blue">
              The TLD "{mintInfo.tldName}" is already registered and owned by someone else.
              <Group mt="md">
                <Button
                  variant="outline"
                  onClick={() => router.push(`/info/${encodeURIComponent(mintInfo.tldName)}`)}
                >
                  View TLD Details
                </Button>
              </Group>
            </Alert>
          )}

          {/* TLD Available for Minting */}
          {!loading && !error && mintInfo && !mintInfo.exists && (
            <>
              {/* Mint Information */}
              <SimpleGrid cols={{ base: 1, md: 2 }} gap="lg">
                {/* TLD Details */}
                <Paper shadow="sm" p="lg" radius="md" withBorder>
                  <Stack gap="md">
                    <Group>
                      <ThemeIcon color="orange" size={30}>
                        <IconCoin size={18} />
                      </ThemeIcon>
                      <Text size="lg" fw={500}>TLD Details</Text>
                    </Group>
                    
                    <Stack gap="sm">
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">TLD Name:</Text>
                        <Badge variant="light" color="orange" size="lg">
                          {mintInfo.tldName}
                        </Badge>
                      </Group>

                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Status:</Text>
                        <Badge variant="light" color="green">
                          Available for Minting
                        </Badge>
                      </Group>

                      {mintInfo.mintCost && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Mint Cost:</Text>
                          <Text size="sm" fw={500} c="orange">
                            {formatEther(mintInfo.mintCost)} ETH
                          </Text>
                        </Group>
                      )}

                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Network:</Text>
                        <Text size="sm" fw={500}>Base Sepolia</Text>
                      </Group>
                    </Stack>
                  </Stack>
                </Paper>

                {/* Wallet Status */}
                <Paper shadow="sm" p="lg" radius="md" withBorder>
                  <Stack gap="md">
                    <Group>
                      <ThemeIcon color="blue" size={30}>
                        <IconWallet size={18} />
                      </ThemeIcon>
                      <Text size="lg" fw={500}>Wallet Status</Text>
                    </Group>
                    
                    <Stack gap="sm">
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Connected:</Text>
                        <Badge variant="light" color="green">
                          <IconCheck size={12} /> Yes
                        </Badge>
                      </Group>

                      {balance && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Balance:</Text>
                          <Text size="sm" fw={500}>
                            {parseFloat(balance.formatted).toFixed(4)} ETH
                          </Text>
                        </Group>
                      )}

                      {balance && mintInfo.mintCost && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Sufficient Funds:</Text>
                          <Badge 
                            variant="light" 
                            color={balance.value >= mintInfo.mintCost ? 'green' : 'red'}
                          >
                            {balance.value >= mintInfo.mintCost ? (
                              <><IconCheck size={12} /> Yes</>
                            ) : (
                              <><IconAlertCircle size={12} /> No</>
                            )}
                          </Badge>
                        </Group>
                      )}
                    </Stack>
                  </Stack>
                </Paper>
              </SimpleGrid>

              {/* Benefits Section */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <ThemeIcon color="green" size={30}>
                      <IconTrendingUp size={18} />
                    </ThemeIcon>
                    <Text size="lg" fw={500}>Benefits of Owning This TLD</Text>
                  </Group>

                  <List
                    spacing="sm"
                    size="sm"
                    icon={
                      <ThemeIcon color="green" size={20} radius="xl">
                        <IconCheck size={12} />
                      </ThemeIcon>
                    }
                  >
                    {mintInfo.benefits.map((benefit, index) => (
                      <List.Item key={index}>{benefit}</List.Item>
                    ))}
                  </List>
                </Stack>
              </Paper>

              {/* Requirements Section */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <ThemeIcon color="blue" size={30}>
                      <IconShield size={18} />
                    </ThemeIcon>
                    <Text size="lg" fw={500}>Requirements</Text>
                  </Group>

                  <List
                    spacing="sm"
                    size="sm"
                    icon={
                      <ThemeIcon color="blue" size={20} radius="xl">
                        <IconInfoCircle size={12} />
                      </ThemeIcon>
                    }
                  >
                    {mintInfo.requirements.map((requirement, index) => (
                      <List.Item key={index}>{requirement}</List.Item>
                    ))}
                  </List>
                </Stack>
              </Paper>

              {/* Mint Button */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack align="center" gap="md">
                  <Text size="lg" fw={500} ta="center">
                    Ready to mint TLD "{mintInfo.tldName}"?
                  </Text>

                  <Text size="sm" c="dimmed" ta="center" maw={600}>
                    By minting this TLD, you will become its owner and can start earning commission
                    from domain registrations under this TLD.
                  </Text>

                  <Group justify="center" mt="md">
                    <Button
                      size="xl"
                      variant="gradient"
                      gradient={{ from: 'orange', to: 'red' }}
                      leftSection={<IconCoin size={20} />}
                      onClick={handleMintTld}
                      loading={minting}
                      disabled={
                        minting ||
                        !mintInfo.mintCost ||
                        (balance && balance.value < mintInfo.mintCost)
                      }
                    >
                      {minting
                        ? 'Minting TLD...'
                        : balance && mintInfo.mintCost && balance.value < mintInfo.mintCost
                          ? 'Insufficient Balance'
                          : `Mint TLD (${mintInfo.mintCost ? formatEther(mintInfo.mintCost) : '0'} ETH)`
                      }
                    </Button>
                  </Group>

                  {balance && mintInfo.mintCost && balance.value < mintInfo.mintCost && (
                    <Alert icon={<IconAlertCircle size={16} />} title="Insufficient Balance" color="red" mt="md">
                      You need {formatEther(mintInfo.mintCost)} ETH to mint this TLD, but your current balance is {balance.formatted} ETH.
                    </Alert>
                  )}
                </Stack>
              </Paper>
            </>
          )}
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
