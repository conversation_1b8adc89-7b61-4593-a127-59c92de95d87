'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount, useWalletClient } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  ThemeIcon,
  Button,
  Alert,
  Card,
  Badge,
  Divider,
  Center,
  Loader,
  SimpleGrid,
  TextInput,
  NumberInput,
  Switch,
  Tabs,
} from '@mantine/core';
import {
  IconSettings,
  IconAlertCircle,
  IconCheck,
  IconWallet,
  IconArrowLeft,
  IconCoin,
  IconPercentage,
  IconToggleLeft,
  IconDatabase,
  IconShield,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';
import { notifications } from '@mantine/notifications';
import { ethers } from 'ethers';

interface TldManagementData {
  tldName: string;
  tokenId: string;
  owner: string;
  isOwner: boolean;
  price: bigint;
  commission: bigint;
  isActive: boolean;
  ercToken?: string;
  tokenPrice?: bigint;
}

interface PageProps {
  params: Promise<{
    'tld-name': string;
  }> | {
    'tld-name': string;
  };
}

export default function TldManagementPage({ params }: PageProps) {
  const [tldName, setTldName] = useState<string>('');
  
  // Handle async params (Next.js 15 compatibility)
  useEffect(() => {
    const getParams = async () => {
      try {
        const resolvedParams = params instanceof Promise ? await params : params;
        setTldName(resolvedParams['tld-name']);
      } catch (error) {
        console.error('Error resolving params:', error);
        setTldName('');
      }
    };
    getParams();
  }, [params]);

  const { isConnected, address } = useAccount();
  const { data: walletClient } = useWalletClient();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tldData, setTldData] = useState<TldManagementData | null>(null);
  const [sdkReady, setSdkReady] = useState(false);
  
  // Form states
  const [newPrice, setNewPrice] = useState<string>('');
  const [newCommission, setNewCommission] = useState<number>(0);
  const [newActive, setNewActive] = useState<boolean>(true);
  const [newErcToken, setNewErcToken] = useState<string>('');
  const [newTokenPrice, setNewTokenPrice] = useState<string>('');
  
  // Loading states for individual operations
  const [updatingPrice, setUpdatingPrice] = useState(false);
  const [updatingCommission, setUpdatingCommission] = useState(false);
  const [updatingActive, setUpdatingActive] = useState(false);
  const [updatingErcToken, setUpdatingErcToken] = useState(false);
  const [updatingTokenPrice, setUpdatingTokenPrice] = useState(false);

  const fetchTldData = useCallback(async () => {
    if (!tldName) return;

    try {
      setLoading(true);
      setError(null);

      const decodedTldName = decodeURIComponent(tldName);
      console.log('Fetching TLD management data for:', decodedTldName);

      // Get TLD token ID
      const tokenId = await sdk.tld().getTLDId(decodedTldName);
      
      // Get TLD information
      const [owner, price, commission, isActive, ercToken, tokenPrice] = await Promise.all([
        sdk.tld().getTLDOwner(tokenId),
        sdk.tld().getTLDPrice(tokenId),
        sdk.tld().getCommission(tokenId),
        sdk.tld().isTLDActive(tokenId),
        sdk.tld().getErcToken(tokenId).catch(() => ''),
        sdk.tld().getTokenPrice(tokenId).catch(() => 0n),
      ]);

      const isOwner = address?.toLowerCase() === owner.toLowerCase();

      const managementData: TldManagementData = {
        tldName: decodedTldName,
        tokenId: tokenId.toString(),
        owner,
        isOwner,
        price,
        commission,
        isActive,
        ercToken: ercToken || undefined,
        tokenPrice: tokenPrice || undefined,
      };

      setTldData(managementData);
      
      // Set form initial values
      setNewPrice(ethers.formatEther(price));
      setNewCommission(Number(commission));
      setNewActive(isActive);
      setNewErcToken(ercToken || '');
      setNewTokenPrice(tokenPrice ? ethers.formatEther(tokenPrice) : '');

    } catch (error) {
      console.error('Failed to fetch TLD data:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch TLD information');
    } finally {
      setLoading(false);
    }
  }, [tldName, address]);

  useEffect(() => {
    if (!isConnected) {
      router.push('/');
      return;
    }

    initializeSDK()
      .then(() => setSdkReady(true))
      .catch((err) => setError(err.message));
  }, [isConnected, router]);

  useEffect(() => {
    if (sdkReady && tldName) {
      fetchTldData();
    }
  }, [sdkReady, tldName, fetchTldData]);

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const handleUpdatePrice = async () => {
    if (!tldData || !walletClient || !newPrice) return;
    
    setUpdatingPrice(true);
    try {
      const priceInWei = ethers.parseEther(newPrice);
      const tx = await sdk.tld().connect(walletClient).setTLDPrice(tldData.tokenId, priceInWei);
      await tx.wait();
      
      notifications.show({
        title: 'Price Updated',
        message: `TLD price updated to ${newPrice} ETH`,
        color: 'green',
      });
      
      await fetchTldData(); // Refresh data
    } catch (error) {
      console.error('Failed to update price:', error);
      notifications.show({
        title: 'Update Failed',
        message: error instanceof Error ? error.message : 'Failed to update price',
        color: 'red',
      });
    } finally {
      setUpdatingPrice(false);
    }
  };

  const handleUpdateCommission = async () => {
    if (!tldData || !walletClient) return;
    
    setUpdatingCommission(true);
    try {
      const tx = await sdk.tld().connect(walletClient).setCommission(tldData.tokenId, newCommission);
      await tx.wait();
      
      notifications.show({
        title: 'Commission Updated',
        message: `Commission updated to ${newCommission}%`,
        color: 'green',
      });
      
      await fetchTldData(); // Refresh data
    } catch (error) {
      console.error('Failed to update commission:', error);
      notifications.show({
        title: 'Update Failed',
        message: error instanceof Error ? error.message : 'Failed to update commission',
        color: 'red',
      });
    } finally {
      setUpdatingCommission(false);
    }
  };

  const handleUpdateActive = async () => {
    if (!tldData || !walletClient) return;
    
    setUpdatingActive(true);
    try {
      const tx = await sdk.tld().connect(walletClient).setTLDActive(tldData.tokenId, newActive);
      await tx.wait();
      
      notifications.show({
        title: 'Status Updated',
        message: `TLD ${newActive ? 'activated' : 'deactivated'}`,
        color: 'green',
      });
      
      await fetchTldData(); // Refresh data
    } catch (error) {
      console.error('Failed to update active status:', error);
      notifications.show({
        title: 'Update Failed',
        message: error instanceof Error ? error.message : 'Failed to update status',
        color: 'red',
      });
    } finally {
      setUpdatingActive(false);
    }
  };

  if (!isConnected || !tldName) {
    return null; // Will redirect or still loading params
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => router.back()}
            >
              Back
            </Button>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'violet', to: 'purple' }}>
              <IconSettings size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Manage TLD</Title>
              <Text c="dimmed">Configure settings for "{decodeURIComponent(tldName)}" TLD</Text>
            </div>
          </Group>

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
              {error}
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Loading TLD information...</Text>
              </Stack>
            </Paper>
          )}

          {/* Access Denied */}
          {!loading && !error && tldData && !tldData.isOwner && (
            <Alert icon={<IconShield size={16} />} title="Access Denied" color="orange">
              You are not the owner of this TLD. Only the TLD owner can manage these settings.
              <br />
              <Text size="sm" mt="xs">
                Current owner: {formatAddress(tldData.owner)}
              </Text>
            </Alert>
          )}

          {/* TLD Management Interface */}
          {!loading && !error && tldData && tldData.isOwner && (
            <>
              {/* TLD Overview */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <ThemeIcon color="blue" size={30}>
                      <IconDatabase size={18} />
                    </ThemeIcon>
                    <Text size="lg" fw={500}>TLD Overview</Text>
                  </Group>

                  <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} gap="md">
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">TLD Name</Text>
                        <Text size="sm" fw={500}>{tldData.tldName}</Text>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Token ID</Text>
                        <Text size="sm" fw={500}>{tldData.tokenId}</Text>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Current Price</Text>
                        <Text size="sm" fw={500}>{ethers.formatEther(tldData.price)} ETH</Text>
                      </Stack>
                    </Card>
                    <Card p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed">Status</Text>
                        <Badge variant="light" color={tldData.isActive ? 'green' : 'red'}>
                          {tldData.isActive ? 'Active' : 'Inactive'}
                        </Badge>
                      </Stack>
                    </Card>
                  </SimpleGrid>
                </Stack>
              </Paper>

              {/* Management Tabs */}
              <Tabs defaultValue="pricing" variant="outline">
                <Tabs.List>
                  <Tabs.Tab value="pricing" leftSection={<IconCoin size={16} />}>
                    Pricing
                  </Tabs.Tab>
                  <Tabs.Tab value="commission" leftSection={<IconPercentage size={16} />}>
                    Commission
                  </Tabs.Tab>
                  <Tabs.Tab value="status" leftSection={<IconToggleLeft size={16} />}>
                    Status
                  </Tabs.Tab>
                  <Tabs.Tab value="tokens" leftSection={<IconWallet size={16} />}>
                    Token Settings
                  </Tabs.Tab>
                </Tabs.List>

                {/* Pricing Tab */}
                <Tabs.Panel value="pricing" pt="md">
                  <Paper shadow="sm" p="lg" radius="md" withBorder>
                    <Stack gap="md">
                      <div>
                        <Text size="lg" fw={500} mb="xs">TLD Pricing</Text>
                        <Text size="sm" c="dimmed">
                          Set the price for registering domains under your TLD. This affects how much users pay to mint domains.
                        </Text>
                      </div>

                      <Group align="end">
                        <TextInput
                          label="New Price (ETH)"
                          placeholder="0.1"
                          value={newPrice}
                          onChange={(e) => setNewPrice(e.target.value)}
                          style={{ flex: 1 }}
                        />
                        <Button
                          leftSection={<IconCoin size={16} />}
                          onClick={handleUpdatePrice}
                          loading={updatingPrice}
                          disabled={!newPrice || newPrice === ethers.formatEther(tldData.price)}
                        >
                          Update Price
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                {/* Commission Tab */}
                <Tabs.Panel value="commission" pt="md">
                  <Paper shadow="sm" p="lg" radius="md" withBorder>
                    <Stack gap="md">
                      <div>
                        <Text size="lg" fw={500} mb="xs">Commission Settings</Text>
                        <Text size="sm" c="dimmed">
                          Set the commission percentage you earn from domain registrations under your TLD.
                        </Text>
                      </div>

                      <Group align="end">
                        <NumberInput
                          label="Commission (%)"
                          placeholder="10"
                          value={newCommission}
                          onChange={(value) => setNewCommission(Number(value) || 0)}
                          min={0}
                          max={100}
                          style={{ flex: 1 }}
                        />
                        <Button
                          leftSection={<IconPercentage size={16} />}
                          onClick={handleUpdateCommission}
                          loading={updatingCommission}
                          disabled={newCommission === Number(tldData.commission)}
                        >
                          Update Commission
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                {/* Status Tab */}
                <Tabs.Panel value="status" pt="md">
                  <Paper shadow="sm" p="lg" radius="md" withBorder>
                    <Stack gap="md">
                      <div>
                        <Text size="lg" fw={500} mb="xs">TLD Status</Text>
                        <Text size="sm" c="dimmed">
                          Control whether your TLD is active and accepting new domain registrations.
                        </Text>
                      </div>

                      <Group align="center">
                        <Switch
                          label={newActive ? "TLD is Active" : "TLD is Inactive"}
                          description={newActive ? "Users can register domains under this TLD" : "Domain registration is disabled"}
                          checked={newActive}
                          onChange={(e) => setNewActive(e.currentTarget.checked)}
                        />
                        <Button
                          leftSection={<IconToggleLeft size={16} />}
                          onClick={handleUpdateActive}
                          loading={updatingActive}
                          disabled={newActive === tldData.isActive}
                          color={newActive ? 'green' : 'red'}
                        >
                          {newActive ? 'Activate' : 'Deactivate'} TLD
                        </Button>
                      </Group>
                    </Stack>
                  </Paper>
                </Tabs.Panel>

                {/* Token Settings Tab */}
                <Tabs.Panel value="tokens" pt="md">
                  <Stack gap="md">
                    {/* ERC Token Settings */}
                    <Paper shadow="sm" p="lg" radius="md" withBorder>
                      <Stack gap="md">
                        <div>
                          <Text size="lg" fw={500} mb="xs">ERC Token Address</Text>
                          <Text size="sm" c="dimmed">
                            Set a custom ERC-20 token address for payments (optional).
                          </Text>
                        </div>

                        <Group align="end">
                          <TextInput
                            label="Token Contract Address"
                            placeholder="0x..."
                            value={newErcToken}
                            onChange={(e) => setNewErcToken(e.target.value)}
                            style={{ flex: 1 }}
                          />
                          <Button
                            leftSection={<IconWallet size={16} />}
                            onClick={async () => {
                              if (!tldData || !walletClient) return;
                              setUpdatingErcToken(true);
                              try {
                                const tx = await sdk.tld().connect(walletClient).setErcToken(tldData.tokenId, newErcToken);
                                await tx.wait();
                                notifications.show({
                                  title: 'Token Address Updated',
                                  message: 'ERC token address updated successfully',
                                  color: 'green',
                                });
                                await fetchTldData();
                              } catch (error) {
                                notifications.show({
                                  title: 'Update Failed',
                                  message: error instanceof Error ? error.message : 'Failed to update token address',
                                  color: 'red',
                                });
                              } finally {
                                setUpdatingErcToken(false);
                              }
                            }}
                            loading={updatingErcToken}
                            disabled={newErcToken === (tldData.ercToken || '')}
                          >
                            Update Token
                          </Button>
                        </Group>
                      </Stack>
                    </Paper>

                    {/* Token Price Settings */}
                    <Paper shadow="sm" p="lg" radius="md" withBorder>
                      <Stack gap="md">
                        <div>
                          <Text size="lg" fw={500} mb="xs">Token Price</Text>
                          <Text size="sm" c="dimmed">
                            Set the price in tokens for domain registration (if using ERC-20 tokens).
                          </Text>
                        </div>

                        <Group align="end">
                          <TextInput
                            label="Token Price"
                            placeholder="100"
                            value={newTokenPrice}
                            onChange={(e) => setNewTokenPrice(e.target.value)}
                            style={{ flex: 1 }}
                          />
                          <Button
                            leftSection={<IconCoin size={16} />}
                            onClick={async () => {
                              if (!tldData || !walletClient || !newTokenPrice) return;
                              setUpdatingTokenPrice(true);
                              try {
                                const priceInWei = ethers.parseEther(newTokenPrice);
                                const tx = await sdk.tld().connect(walletClient).setTokenPrice(tldData.tokenId, priceInWei);
                                await tx.wait();
                                notifications.show({
                                  title: 'Token Price Updated',
                                  message: `Token price updated to ${newTokenPrice}`,
                                  color: 'green',
                                });
                                await fetchTldData();
                              } catch (error) {
                                notifications.show({
                                  title: 'Update Failed',
                                  message: error instanceof Error ? error.message : 'Failed to update token price',
                                  color: 'red',
                                });
                              } finally {
                                setUpdatingTokenPrice(false);
                              }
                            }}
                            loading={updatingTokenPrice}
                            disabled={!newTokenPrice || (tldData.tokenPrice && newTokenPrice === ethers.formatEther(tldData.tokenPrice))}
                          >
                            Update Price
                          </Button>
                        </Group>
                      </Stack>
                    </Paper>
                  </Stack>
                </Tabs.Panel>
              </Tabs>
            </>
          )}
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
