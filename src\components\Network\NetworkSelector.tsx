'use client';

import { useState, useEffect } from 'react';
import { Select, Group, Text, Badge, Loader } from '@mantine/core';
import { IconNetwork } from '@tabler/icons-react';
import { useChainId, useSwitch<PERSON>hain } from 'wagmi';
import {
  displayNetworkList,
  setSelectedNetwork as saveSelectedNetwork,
  isSDKReady,
  getODudeNetworkFromChainId,
  getChainIdFromODudeNetwork
} from '@/lib/odude';
import { notifications } from '@mantine/notifications';

interface NetworkOption {
  value: string;
  label: string;
  isConnected: boolean;
  hasContracts: boolean;
}

interface NetworkSelectorProps {
  onNetworkChange?: (networkKey: string) => void;
}

export function NetworkSelector({ onNetworkChange }: NetworkSelectorProps) {
  const [selectedNetwork, setSelectedNetwork] = useState<string | null>(null);
  const [networkOptions, setNetworkOptions] = useState<NetworkOption[]>([]);
  const [loading, setLoading] = useState(true);

  const chainId = useChainId();
  const { switchChain } = useSwitchChain();

  useEffect(() => {
    const loadNetworks = () => {
      try {
        if (!isSDKReady()) {
          setLoading(true);
          return;
        }

        const networkInfo = displayNetworkList();

        // Convert supported networks to select options
        const options: NetworkOption[] = Object.entries(networkInfo.supportedNetworks).map(
          ([key, network]: [string, { name: string; isConnected: boolean; hasContracts: boolean }]) => ({
            value: key,
            label: network.name,
            isConnected: network.isConnected,
            hasContracts: network.hasContracts,
          })
        );

        setNetworkOptions(options);

        // Sync with wallet chain if available
        const walletNetwork = getODudeNetworkFromChainId(chainId);
        if (walletNetwork && options.find(opt => opt.value === walletNetwork)) {
          setSelectedNetwork(walletNetwork);
          saveSelectedNetwork(walletNetwork);
        } else {
          setSelectedNetwork(networkInfo.currentNetwork);
        }

        setLoading(false);
      } catch (error) {
        console.error('Failed to load networks:', error);
        setLoading(false);
      }
    };

    // Load networks initially
    loadNetworks();

    // Set up interval to refresh network status
    const interval = setInterval(loadNetworks, 5000);

    return () => clearInterval(interval);
  }, [chainId]);

  const handleNetworkChange = async (value: string | null) => {
    if (!value || value === selectedNetwork) return;

    try {
      setLoading(true);

      // Update the selected network in session storage
      saveSelectedNetwork(value);
      setSelectedNetwork(value);

      // Try to switch wallet chain if mapping exists
      const chainIdToSwitch = getChainIdFromODudeNetwork(value);
      if (chainIdToSwitch && switchChain) {
        try {
          await switchChain({ chainId: chainIdToSwitch });
        } catch (switchError) {
          console.warn('Failed to switch wallet chain:', switchError);
          // Continue anyway - user can manually switch wallet
        }
      }

      // Call the callback if provided
      if (onNetworkChange) {
        onNetworkChange(value);
      }

      notifications.show({
        title: 'Network Selected',
        message: `Switched to ${networkOptions.find(opt => opt.value === value)?.label}`,
        color: 'blue',
      });

      setLoading(false);
    } catch (error) {
      console.error('Failed to switch network:', error);
      notifications.show({
        title: 'Network Switch Failed',
        message: 'Failed to switch network. Please try again.',
        color: 'red',
      });
      setLoading(false);
    }
  };

  const renderSelectOption = (option: NetworkOption) => (
    <Group justify="space-between" w="100%">
      <Group gap="xs">
        <IconNetwork size={16} />
        <Text size="sm">{option.label}</Text>
      </Group>
      <Group gap="xs">
        {option.isConnected && (
          <Badge size="xs" variant="light" c="green">
            Connected
          </Badge>
        )}
        {option.hasContracts && (
          <Badge size="xs" variant="outline" c="blue">
            Deployed
          </Badge>
        )}
      </Group>
    </Group>
  );

  if (loading && networkOptions.length === 0) {
    return (
      <Group gap="xs">
        <Loader size="sm" />
        <Text size="sm" c="dimmed">Loading networks...</Text>
      </Group>
    );
  }

  return (
    <Select
      placeholder="Select Network"
      data={networkOptions.map(option => ({
        value: option.value,
        label: option.label,
      }))}
      value={selectedNetwork}
      onChange={handleNetworkChange}
      leftSection={<IconNetwork size={16} />}
      disabled={loading}
      w={200}
      renderOption={({ option }) => {
        const networkOption = networkOptions.find(opt => opt.value === option.value);
        return networkOption ? renderSelectOption(networkOption) : null;
      }}
    />
  );
}
