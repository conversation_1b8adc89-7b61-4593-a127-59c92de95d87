'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  Badge,
  ThemeIcon,
  Loader,
  Alert,
  SimpleGrid,
  Card,
  Button,
  CopyButton,
  ActionIcon,
  Tooltip,
} from '@mantine/core';
import {
  IconInfoCircle,
  IconAlertCircle,
  IconCheck,
  IconCopy,
  IconArrowLeft,
  IconUser,
  IconNetwork,
  IconCalendar,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';

interface NameInfoData {
  name: string;
  tokenId?: string;
  owner?: string;
  resolvedAddress?: string;
  registrationDate?: string;
  expirationDate?: string;
  metadata?: Record<string, unknown>;
  tldInfo?: Record<string, unknown>;
}

interface PageProps {
  params: Promise<{
    name: string;
  }> | {
    name: string;
  };
}

export default function NameInfoPage({ params }: PageProps) {
  const [name, setName] = useState<string>('');

  // Handle async params (Next.js 15 compatibility)
  useEffect(() => {
    const getParams = async () => {
      try {
        // Check if params is a Promise
        const resolvedParams = params instanceof Promise ? await params : params;
        setName(resolvedParams.name);
      } catch (error) {
        console.error('Error resolving params:', error);
        setName('');
      }
    };
    getParams();
  }, [params]);
  const { isConnected } = useAccount();
  const router = useRouter();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [nameInfo, setNameInfo] = useState<NameInfoData | null>(null);
  const [sdkReady, setSdkReady] = useState(false);

  const fetchNameInfo = useCallback(async () => {
    if (!name) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const decodedName = decodeURIComponent(name);
      console.log('Fetching info for:', decodedName);

      let nameData = null;
      let tldData = null;

      // Try to get name information
      try {
        nameData = await sdk.getNameInfo(decodedName);
      } catch (nameError) {
        console.warn('Failed to fetch name info:', nameError);
        // If name doesn't exist, we'll still try to get TLD info
      }

      // Check if it's a TLD (no @ symbol) or has a TLD part
      if (decodedName.includes('@')) {
        const tldPart = decodedName.split('@')[1];
        if (tldPart) {
          try {
            tldData = await sdk.getTldInfo(tldPart);
          } catch (tldError) {
            console.warn('Failed to fetch TLD info:', tldError);
          }
        }
      } else {
        // It might be a TLD itself
        try {
          tldData = await sdk.getTldInfo(decodedName);
        } catch (tldError) {
          console.warn('Failed to fetch TLD info:', tldError);
        }
      }

      // If we have neither name data nor TLD data, show an error
      if (!nameData && !tldData) {
        setError(`No information found for "${decodedName}". It may not exist or be registered yet.`);
        setNameInfo(null);
        return;
      }

      setNameInfo({
        name: decodedName,
        tokenId: nameData?.tokenId?.toString(),
        owner: nameData?.owner,
        resolvedAddress: nameData?.resolvedAddress,
        registrationDate: (nameData as any)?.registrationDate,
        expirationDate: (nameData as any)?.expirationDate,
        metadata: nameData?.metadata,
        tldInfo: tldData,
      });

    } catch (error) {
      console.error('Failed to fetch name info:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch name information');
    } finally {
      setLoading(false);
    }
  }, [name]);

  useEffect(() => {
    if (!isConnected) {
      router.push('/');
      return;
    }

    initializeSDK()
      .then(() => setSdkReady(true))
      .catch((err) => setError(err.message));
  }, [isConnected, router]);

  useEffect(() => {
    if (sdkReady && name) {
      fetchNameInfo();
    }
  }, [sdkReady, name, fetchNameInfo]);

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (!isConnected || !name) {
    return null; // Will redirect or still loading params
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <Button
              variant="subtle"
              leftSection={<IconArrowLeft size={16} />}
              onClick={() => router.back()}
            >
              Back
            </Button>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'blue', to: 'cyan' }}>
              <IconInfoCircle size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Name Information</Title>
              <Text c="dimmed">Detailed information for {decodeURIComponent(name)}</Text>
            </div>
          </Group>

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
              {error}
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Loading name information...</Text>
              </Stack>
            </Paper>
          )}

          {/* Name Information */}
          {!loading && !error && nameInfo && (
            <SimpleGrid cols={{ base: 1, md: 2 }} gap="lg">
              {/* Basic Information */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <ThemeIcon color="blue" size={30}>
                      <IconUser size={18} />
                    </ThemeIcon>
                    <Text size="lg" fw={500}>Basic Information</Text>
                  </Group>
                  
                  <Stack gap="sm">
                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Name:</Text>
                      <Text size="sm" fw={500}>{nameInfo.name}</Text>
                    </Group>

                    {nameInfo.tokenId && (
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Token ID:</Text>
                        <Text size="sm" fw={500}>{nameInfo.tokenId}</Text>
                      </Group>
                    )}

                    {nameInfo.owner && (
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Owner:</Text>
                        <Group gap="xs">
                          <Text size="sm" fw={500}>{formatAddress(nameInfo.owner)}</Text>
                          <CopyButton value={nameInfo.owner}>
                            {({ copied, copy }) => (
                              <Tooltip label={copied ? 'Copied' : 'Copy address'}>
                                <ActionIcon
                                  color={copied ? 'teal' : 'gray'}
                                  variant="subtle"
                                  onClick={copy}
                                  size="sm"
                                >
                                  {copied ? <IconCheck size={12} /> : <IconCopy size={12} />}
                                </ActionIcon>
                              </Tooltip>
                            )}
                          </CopyButton>
                        </Group>
                      </Group>
                    )}

                    {nameInfo.resolvedAddress && (
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Resolves to:</Text>
                        <Group gap="xs">
                          <Text size="sm" fw={500}>{formatAddress(nameInfo.resolvedAddress)}</Text>
                          <CopyButton value={nameInfo.resolvedAddress}>
                            {({ copied, copy }) => (
                              <Tooltip label={copied ? 'Copied' : 'Copy address'}>
                                <ActionIcon
                                  color={copied ? 'teal' : 'gray'}
                                  variant="subtle"
                                  onClick={copy}
                                  size="sm"
                                >
                                  {copied ? <IconCheck size={12} /> : <IconCopy size={12} />}
                                </ActionIcon>
                              </Tooltip>
                            )}
                          </CopyButton>
                        </Group>
                      </Group>
                    )}
                  </Stack>
                </Stack>
              </Paper>

              {/* Registration Information */}
              <Paper shadow="sm" p="lg" radius="md" withBorder>
                <Stack gap="md">
                  <Group>
                    <ThemeIcon color="green" size={30}>
                      <IconCalendar size={18} />
                    </ThemeIcon>
                    <Text size="lg" fw={500}>Registration Details</Text>
                  </Group>
                  
                  <Stack gap="sm">
                    {nameInfo.registrationDate && (
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Registered:</Text>
                        <Text size="sm" fw={500}>{formatDate(nameInfo.registrationDate)}</Text>
                      </Group>
                    )}

                    {nameInfo.expirationDate && (
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Expires:</Text>
                        <Text size="sm" fw={500}>{formatDate(nameInfo.expirationDate)}</Text>
                      </Group>
                    )}

                    <Group justify="space-between">
                      <Text size="sm" c="dimmed">Status:</Text>
                      <Badge variant="light" color={nameInfo.owner ? 'blue' : 'gray'}>
                        {nameInfo.owner ? 'Registered' : 'Available'}
                      </Badge>
                    </Group>
                  </Stack>
                </Stack>
              </Paper>
            </SimpleGrid>
          )}

          {/* TLD Information */}
          {!loading && !error && nameInfo?.tldInfo && (
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Stack gap="md">
                <Group>
                  <ThemeIcon color="violet" size={30}>
                    <IconNetwork size={18} />
                  </ThemeIcon>
                  <Text size="lg" fw={500}>TLD Information</Text>
                </Group>
                
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} gap="md">
                  {Object.entries(nameInfo.tldInfo).map(([key, value]: [string, unknown]) => (
                    <Card key={key} p="md" withBorder>
                      <Stack gap="xs">
                        <Text size="sm" c="dimmed" tt="capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}:</Text>
                        <Text size="sm" fw={500}>
                          {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                        </Text>
                      </Stack>
                    </Card>
                  ))}
                </SimpleGrid>
              </Stack>
            </Paper>
          )}

          {/* Metadata */}
          {!loading && !error && nameInfo?.metadata && (
            <Paper shadow="sm" p="lg" radius="md" withBorder>
              <Stack gap="md">
                <Text size="lg" fw={500}>Metadata</Text>
                <Card p="md" withBorder>
                  <pre style={{ fontSize: '12px', overflow: 'auto' }}>
                    {JSON.stringify(nameInfo.metadata, null, 2)}
                  </pre>
                </Card>
              </Stack>
            </Paper>
          )}

          {/* No Data State */}
          {!loading && !error && !nameInfo && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Stack align="center" gap="md">
                <ThemeIcon size={60} radius="xl" color="gray">
                  <IconInfoCircle size={30} />
                </ThemeIcon>
                <div style={{ textAlign: 'center' }}>
                  <Text size="lg" fw={500}>No Information Found</Text>
                  <Text c="dimmed" mt="xs">
                    No information available for this name.
                  </Text>
                </div>
              </Stack>
            </Paper>
          )}
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
