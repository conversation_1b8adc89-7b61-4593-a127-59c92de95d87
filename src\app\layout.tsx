'use client'

import { MantineProvider } from '@mantine/core'
import { Notifications } from '@mantine/notifications'
import { WagmiProvider } from 'wagmi'
import { RainbowKitProvider } from '@rainbow-me/rainbowkit'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { config } from '@/lib/wagmi'
import { theme } from '@/theme/theme'

// Import styles
import '@mantine/core/styles.css'
import '@mantine/notifications/styles.css'
import '@rainbow-me/rainbowkit/styles.css'
import './globals.css'

const queryClient = new QueryClient()

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <head>
        <title>ODude Manager - Web3 Dashboard</title>
        <meta name="description" content="Web3 Dashboard for ODude Name Service" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </head>
      <body>
        <QueryClientProvider client={queryClient}>
          <WagmiProvider config={config}>
            <RainbowKitProvider>
              <MantineProvider theme={theme} defaultColorScheme="dark">
                <Notifications />
                {children}
              </MantineProvider>
            </RainbowKitProvider>
          </WagmiProvider>
        </QueryClientProvider>
      </body>
    </html>
  )
}
