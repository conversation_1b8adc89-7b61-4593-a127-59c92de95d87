{"name": "odude-manager", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@mantine/core": "^8.3.3", "@mantine/hooks": "^8.1.2", "@mantine/next": "^6.0.22", "@mantine/notifications": "^8.1.2", "@odude/odude-sdk": "^1.0.4", "@rainbow-me/rainbowkit": "^2.2.8", "@tabler/icons-react": "^3.35.0", "@tanstack/react-query": "^5.90.2", "ethers": "^5.8.0", "next": "15.5.4", "react": "19.1.0", "react-dom": "19.1.0", "viem": "^2.38.0", "wagmi": "^2.17.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.4", "tailwindcss": "^4", "typescript": "^5"}}